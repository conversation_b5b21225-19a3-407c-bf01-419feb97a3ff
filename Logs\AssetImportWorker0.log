Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-11T05:54:54Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Team/HappinessBox
-logFile
Logs/AssetImportWorker0.log
-srvPort
2035
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Team/HappinessBox
D:/Unity/Team/HappinessBox
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20932]  Target information:

Player connection [20932]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3677847193 [EditorId] 3677847193 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20932]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3677847193 [EditorId] 3677847193 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20932] Host joined multi-casting on [***********:54997]...
Player connection [20932] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 10.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Team/HappinessBox/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:   Intel
    VRAM:     6107 MB
    Driver:   31.0.101.2130
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56892
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004080 seconds.
- Loaded All Assemblies, in  1.038 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 689 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.873 seconds
Domain Reload Profiling: 2911ms
	BeginReloadAssembly (400ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (431ms)
		LoadAssemblies (401ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (418ms)
			TypeCache.Refresh (414ms)
				TypeCache.ScanAssembly (382ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1874ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1723ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1030ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (385ms)
			ProcessInitializeOnLoadMethodAttributes (191ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.429 seconds
Refreshing native plugins compatible for Editor in 2.55 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.249 seconds
Domain Reload Profiling: 4673ms
	BeginReloadAssembly (302ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (451ms)
	RebuildNativeTypeToScriptingClass (44ms)
	initialDomainReloadingComplete (177ms)
	LoadAllAssembliesAndSetupDomain (1449ms)
		LoadAssemblies (820ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (808ms)
			TypeCache.Refresh (675ms)
				TypeCache.ScanAssembly (633ms)
			BuildScriptInfoCaches (112ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (2250ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1950ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (242ms)
			ProcessInitializeOnLoadAttributes (1565ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 3.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5837 unused Assets / (2.2 MB). Loaded Objects now: 6381.
Memory consumption went from 112.2 MB to 110.0 MB.
Total: 16.812900 ms (FindLiveObjects: 2.460100 ms CreateObjectMapping: 2.751300 ms MarkObjects: 8.559200 ms  DeleteObjects: 3.040800 ms)

========================================================================
Received Import Request.
  Time since last request: 492398.325377 seconds.
  path: Assets/Scenes/Puzzle.unity
  artifactKey: Guid(4f41eebfba8f0724bb73667d2008deab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Puzzle.unity using Guid(4f41eebfba8f0724bb73667d2008deab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77aa19dae5ea6605b49d1f6292f290e1') in 0.0118302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Scenes/Drawing.unity
  artifactKey: Guid(87ffc7c5dd4646342b27745d01580805) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Drawing.unity using Guid(87ffc7c5dd4646342b27745d01580805) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4c975dcc9b02ea48257787cc0ab515f') in 0.0009664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.056439 seconds.
  path: Assets/Scenes/Main.unity
  artifactKey: Guid(2cda990e2423bbf4892e6590ba056729) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Main.unity using Guid(2cda990e2423bbf4892e6590ba056729) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d0da0bb7a8b45eab80506f1e5a801af') in 0.0053789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 331.755973 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/7.png
  artifactKey: Guid(a54c1401056d61d4589c6d80ea285a33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/7.png using Guid(a54c1401056d61d4589c6d80ea285a33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bdd3f81d7ec1954157552a112aa679f') in 0.4047193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/1.png
  artifactKey: Guid(2152fe4a4d4229442a12ff77b7dfcd36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/1.png using Guid(2152fe4a4d4229442a12ff77b7dfcd36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f87abb5a7769874dac1d54e2ab735004') in 0.6064928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000439 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/2.png
  artifactKey: Guid(e51cbbe3dfa849749acf11a77351cd23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/2.png using Guid(e51cbbe3dfa849749acf11a77351cd23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dfa498540c1fd652d1ae8f08c04e9c69') in 0.1724379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/6.png
  artifactKey: Guid(0f51e0ce4f63ee44691cce81cd628a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/6.png using Guid(0f51e0ce4f63ee44691cce81cd628a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '910774b0d5ac4bfadef5ac71c77faeaf') in 0.0847652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000133 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/4.png
  artifactKey: Guid(02fd4a4d22f27514facbc3fac99b5354) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/4.png using Guid(02fd4a4d22f27514facbc3fac99b5354) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34cdba515a5b84b35551b7741b8c2195') in 0.0746145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.001302 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/9.png
  artifactKey: Guid(9c8e62292b2b4f847984ce1ce6c14f53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/9.png using Guid(9c8e62292b2b4f847984ce1ce6c14f53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '034aca570022001a548b109db9b8c720') in 0.0880704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000495 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/5.png
  artifactKey: Guid(27668542163030f46b4df6a2f5f3d52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/5.png using Guid(27668542163030f46b4df6a2f5f3d52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa25467c257feb382bd27faaf9be7121') in 0.085578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/8.png
  artifactKey: Guid(2108e770c82544640bcd52e2a79414f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/8.png using Guid(2108e770c82544640bcd52e2a79414f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb714018567308e204eadc05f767a262') in 0.0789898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000807 seconds.
  path: Assets/Art/app/Icons/koodakaaneh/3.png
  artifactKey: Guid(d58a54ef1ee820c4eb42c4d741854426) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/app/Icons/koodakaaneh/3.png using Guid(d58a54ef1ee820c4eb42c4d741854426) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef5404a020dbec0c35dd22b0a8f046b9') in 0.1125061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 388.355106 seconds.
  path: Assets/FlappyBird/Materials/Background.mat
  artifactKey: Guid(c231a489ac80afe46985629f5ad46f4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/FlappyBird/Materials/Background.mat using Guid(c231a489ac80afe46985629f5ad46f4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e1c2d62fc8cd145663380a7f5ceea08') in 0.1912837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/FlappyBird/Materials/Ground.mat
  artifactKey: Guid(86937624cf8a06e4eb8ad96f309f2cc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/FlappyBird/Materials/Ground.mat using Guid(86937624cf8a06e4eb8ad96f309f2cc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95ea616e6cb428cc9c4b0f14295fed25') in 0.0383258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 269.599030 seconds.
  path: Assets/Prefabs/Main/Player(Clone).prefab
  artifactKey: Guid(946cbe70f2d4c374f9a810a13a4fccec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Main/Player(Clone).prefab using Guid(946cbe70f2d4c374f9a810a13a4fccec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f73fe12f77321c023a290b3807bdfe08') in 0.0815248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 20.021051 seconds.
  path: Assets/Prefabs/Main/Player.prefab
  artifactKey: Guid(b8e23955d3d7d034d8e3e6c77109b07c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Main/Player.prefab using Guid(b8e23955d3d7d034d8e3e6c77109b07c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '01e03e22d6a01dd8eacd5ba7f74ae862') in 0.0789077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 179.617 seconds
Refreshing native plugins compatible for Editor in 11.50 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.170 seconds
Domain Reload Profiling: 187791ms
	BeginReloadAssembly (4103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (532ms)
	RebuildCommonClasses (1353ms)
	RebuildNativeTypeToScriptingClass (103ms)
	initialDomainReloadingComplete (209ms)
	LoadAllAssembliesAndSetupDomain (173850ms)
		LoadAssemblies (129153ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (47742ms)
			TypeCache.Refresh (4751ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (42905ms)
			ResolveRequiredComponents (61ms)
	FinalizeReload (8172ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6936ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1008ms)
			SetLoadedEditorAssemblies (216ms)
			BeforeProcessingInitializeOnLoad (2430ms)
			ProcessInitializeOnLoadAttributes (3128ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 14.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5838 unused Assets / (497.6 KB). Loaded Objects now: 6433.
Memory consumption went from 105.8 MB to 105.3 MB.
Total: 49.650300 ms (FindLiveObjects: 4.615400 ms CreateObjectMapping: 4.355500 ms MarkObjects: 36.137800 ms  DeleteObjects: 4.539300 ms)

Prepare: number of updated asset objects reloaded= 0
