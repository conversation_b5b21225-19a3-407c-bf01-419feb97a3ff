Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-06T15:19:47Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Team/HappinessBox
-logFile
Logs/AssetImportWorker0.log
-srvPort
13168
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Team/HappinessBox
D:/Unity/Team/HappinessBox
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18764]  Target information:

Player connection [18764]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1045799539 [EditorId] 1045799539 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18764]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1045799539 [EditorId] 1045799539 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18764] Host joined multi-casting on [***********:54997]...
Player connection [18764] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Team/HappinessBox/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:   Intel
    VRAM:     6107 MB
    Driver:   31.0.101.2130
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56924
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.008988 seconds.
- Loaded All Assemblies, in  1.415 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 852 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.571 seconds
Domain Reload Profiling: 3985ms
	BeginReloadAssembly (488ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (128ms)
	RebuildNativeTypeToScriptingClass (53ms)
	initialDomainReloadingComplete (223ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (487ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (511ms)
			TypeCache.Refresh (507ms)
				TypeCache.ScanAssembly (465ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2572ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2313ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1178ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (565ms)
			ProcessInitializeOnLoadMethodAttributes (383ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.639 seconds
Refreshing native plugins compatible for Editor in 6.70 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 16.946 seconds
Domain Reload Profiling: 19585ms
	BeginReloadAssembly (494ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (128ms)
	RebuildNativeTypeToScriptingClass (53ms)
	initialDomainReloadingComplete (132ms)
	LoadAllAssembliesAndSetupDomain (1827ms)
		LoadAssemblies (1105ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1017ms)
			TypeCache.Refresh (831ms)
				TypeCache.ScanAssembly (775ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (16951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (16501ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (2024ms)
			ProcessInitializeOnLoadAttributes (13901ms)
			ProcessInitializeOnLoadMethodAttributes (513ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 6.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5837 unused Assets / (1.5 MB). Loaded Objects now: 6381.
Memory consumption went from 112.2 MB to 110.7 MB.
Total: 50.419500 ms (FindLiveObjects: 2.742700 ms CreateObjectMapping: 2.146000 ms MarkObjects: 36.316900 ms  DeleteObjects: 9.211100 ms)

========================================================================
Received Import Request.
  Time since last request: 94274.062447 seconds.
  path: Assets/SO/Audio/AudioObject.asset
  artifactKey: Guid(977101adc85ae8448b380d086125b91a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SO/Audio/AudioObject.asset using Guid(977101adc85ae8448b380d086125b91a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0aa4f4b85ec1b34d5b3bcdd1313dea8b') in 0.2235674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 68

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.067 seconds
Refreshing native plugins compatible for Editor in 5.78 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.708 seconds
Domain Reload Profiling: 5777ms
	BeginReloadAssembly (1393ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (504ms)
	RebuildCommonClasses (195ms)
	RebuildNativeTypeToScriptingClass (55ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (1340ms)
		LoadAssemblies (1229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (431ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (2709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (370ms)
			ProcessInitializeOnLoadAttributes (1650ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5835 unused Assets / (2.0 MB). Loaded Objects now: 6390.
Memory consumption went from 105.4 MB to 103.4 MB.
Total: 16.279000 ms (FindLiveObjects: 2.177400 ms CreateObjectMapping: 1.760700 ms MarkObjects: 8.536800 ms  DeleteObjects: 3.802400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.868 seconds
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.143 seconds
Domain Reload Profiling: 11012ms
	BeginReloadAssembly (1526ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (160ms)
	RebuildCommonClasses (358ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (134ms)
	LoadAllAssembliesAndSetupDomain (6822ms)
		LoadAssemblies (7088ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (973ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (934ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (2144ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1889ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (289ms)
			ProcessInitializeOnLoadAttributes (1473ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 18.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5835 unused Assets / (2.1 MB). Loaded Objects now: 6395.
Memory consumption went from 105.4 MB to 103.3 MB.
Total: 26.694500 ms (FindLiveObjects: 1.675800 ms CreateObjectMapping: 2.872300 ms MarkObjects: 18.450600 ms  DeleteObjects: 3.693400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2619.021356 seconds.
  path: Assets/Scripts/Tools/AddressableSceneLoader.cs
  artifactKey: Guid(c551418fb2e3cfe45bb0d1c93162313e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Tools/AddressableSceneLoader.cs using Guid(c551418fb2e3cfe45bb0d1c93162313e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '132c8180f19e736d76ffd363e91155da') in 0.0058266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.704244 seconds.
  path: Assets/Scripts/Tools/AdaptiveGridLayoutGroup.cs
  artifactKey: Guid(91edc801959cee24d9f849a9fa18e5a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Tools/AdaptiveGridLayoutGroup.cs using Guid(91edc801959cee24d9f849a9fa18e5a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ff1862210e0570d21fe4dd3bb4970c6') in 0.0020412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1001.230993 seconds.
  path: Assets/Scripts/DownloadHandler/DownloadHandler.cs
  artifactKey: Guid(b846e7bfd575e90479c192645c348e60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/DownloadHandler/DownloadHandler.cs using Guid(b846e7bfd575e90479c192645c348e60) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d495e709636927483e69362f9f07be7') in 0.001944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.771 seconds
Refreshing native plugins compatible for Editor in 4.83 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.817 seconds
Domain Reload Profiling: 5591ms
	BeginReloadAssembly (636ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (47ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (235ms)
	RebuildCommonClasses (102ms)
	RebuildNativeTypeToScriptingClass (48ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1916ms)
		LoadAssemblies (1124ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1040ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (978ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (2818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (457ms)
			ProcessInitializeOnLoadAttributes (1819ms)
			ProcessInitializeOnLoadMethodAttributes (180ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5835 unused Assets / (1.2 MB). Loaded Objects now: 6400.
Memory consumption went from 105.5 MB to 104.3 MB.
Total: 18.032300 ms (FindLiveObjects: 1.057800 ms CreateObjectMapping: 1.945500 ms MarkObjects: 12.438500 ms  DeleteObjects: 2.589200 ms)

Prepare: number of updated asset objects reloaded= 0
