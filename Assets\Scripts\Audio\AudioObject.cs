using System.Collections.Generic;
using System.Linq;
using Data.MiniGame;
using UnityEngine;
using UnityEngine.Localization;

namespace Data.Global
{
    [CreateAssetMenu(fileName = "Category Audio Data", menuName = "Scriptable Objects/Audio/Category Audio Data")]
    public class AudioObject : ScriptableObject
    {
        public List<AudioData> audioData;
    }

    [System.Serializable]
    public class AudioData
    {
        public EmptyVariable categoryId;
        public AudioClipData[] audios;
    }

    [System.Serializable]
    public class AudioClipData
    {
        public AudioClip clip;
        public LocalizedString title;
        public Sprite icon;
        public MiniGameName chosenMiniGames = MiniGameName.FlappyBird;
        public List<MiniGameData> miniGameData = new();
    }

    public static class AudioObjectExtensions
    {
        public static IEnumerable<AudioClipData> GetAllAudioClipData(this IEnumerable<AudioObject> audioObjects)
        {
            return audioObjects
                .SelectMany(ao => ao.audioData)
                .SelectMany(ad => ad.audios);
        }

        public static IEnumerable<AudioClipData> GetAudioClipDataByCategory(this IEnumerable<AudioObject> audioObjects, EmptyVariable categoryId)
        {
            return audioObjects
                .SelectMany(ao => ao.audioData)
                .Where(ad => ad.categoryId == categoryId)
                .SelectMany(ad => ad.audios);
        }
    }

    [System.Flags]
    public enum MiniGameName
    {
        None = 0,
        Puzzle = 1,
        Drawing = 2,
        FlappyBird = 3
    }
}