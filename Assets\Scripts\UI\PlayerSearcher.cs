using System.Collections.Generic;
using System.Linq;
using Data.Global;
using Logic.Global;
using Logic.MiniGame;
using RTLTMPro;
using TMPro;
using UnityEngine;
using UnityEngine.Localization.Components;
using UnityEngine.UI;

namespace Logic.UI
{
    public class PlayerSearcher : MonoBehaviour
    {
        [SerializeField] private GameObject backPrefab, searchButtonPrefab;
        [SerializeField] private GameObject containerPrefab;
        [SerializeField] private EmptyVariable contentId;
        [SerializeField] private SimpleMenuContainer rootContainerTemplate;
        [SerializeField] private List<AudioObject> data;
        [SerializeField] private RectTransform spawnTransform, searchButtonTransform;
        private RectTransform content;
        [SerializeField] private SongTemplate songTemplate;
        private TMP_InputField searchField;
        private GameObject rootContainer;
        private Button search;
        private Button back;
        private RectTransformAnimation closeAnimation;

        private void Start()
        {
            UniversalUIElement rootContainerElement = Instantiate
            (rootContainerTemplate.prefab, spawnTransform).GetComponent<UniversalUIElement>();

            rootContainer = rootContainerElement.gameObject;

            searchField = rootContainer.GetComponentInChildren<TMP_InputField>();

            searchField.onValueChanged.AddListener(Search);

            UniversalUIElement containerElement = Instantiate
            (containerPrefab, rootContainer.transform).GetComponent<UniversalUIElement>();

            UniversalUIElement searchElement = Instantiate
            (searchButtonPrefab, searchButtonTransform).GetComponent<UniversalUIElement>();

            // UniversalUIElement backElement = Instantiate
            // (backPrefab, rootContainer.transform).GetComponent<UniversalUIElement>();

            content = containerElement.GetUIComponent<RectTransform>(contentId);

            RectTransformAnimation open =
            rootContainerElement.GetUIComponent<RectTransformAnimation>(rootContainerTemplate.openId);

            closeAnimation =
            rootContainerElement.GetUIComponent<RectTransformAnimation>(rootContainerTemplate.closeId);

            search = searchElement.GetComponentInChildren<Button>();
            search.onClick.AddListener(() => ShowSearch(open));

            // back = backElement.GetComponentInChildren<Button>();
            // back.onClick.AddListener(() => HideSearch(closeAnimation));

            rootContainer.SetActive(false);
        }

        private void Update()
        {
            if (rootContainer != null && rootContainer.activeInHierarchy)
            {
                if (UnityEngine.InputSystem.Keyboard.current.escapeKey.wasPressedThisFrame)
                {
                    closeAnimation.Play();
                }
            }
        }

        private void ShowSearch(RectTransformAnimation animation)
        {
            rootContainer.SetActive(true);
            animation.Play();
            rootContainer.transform.SetAsLastSibling();
        }

        private void HideSearch(RectTransformAnimation animation)
        {
            animation.Play();
        }

        public void Search(string playerName)
        {
            List<AudioClipData> allAudio = data.GetAllAudioClipData().ToList();

            for (int i = content.childCount - 1; i >= 0; i--)
            {
                Destroy(content.GetChild(i).gameObject);
            }

            for (int i = 0; i < allAudio.Count; i++)
            {
                var audioClip = allAudio[i];
                string localizedClipName = audioClip.title.GetLocalizedString();

                //print($"Clip name : {localizedClipName} + search name : {playerName} + contains : {localizedClipName.Contains(playerName)}");
                if (localizedClipName.Contains(playerName))
                {
                    int index = i;
                    GenerateAudioPlayer(audioClip, content, index);
                }
            }
        }

        private UniversalUIElement GenerateAudioPlayer(AudioClipData clipData, RectTransform spawnTransform, int index = 0)
        {
            UniversalUIElement audioPlayer = Instantiate(songTemplate.prefab, spawnTransform).GetComponent<UniversalUIElement>();

            if (audioPlayer != null)
            {
                SetupUIElement(audioPlayer, clipData, index);
                return audioPlayer;
            }
            else
            {
                Debug.LogError("Failed to create audio player UI element.");
                return null;
            }
        }

        private void SetupUIElement(UniversalUIElement audioPlayer, AudioClipData clipData, int index)
        {
            if (audioPlayer != null)
            {
                Button playButton = audioPlayer.GetUIComponent<Button>(songTemplate.playButtonId);
                Button pauseButton = audioPlayer.GetUIComponent<Button>(songTemplate.pauseButtonId);
                LocalizeStringEvent title = audioPlayer.GetUIComponent<LocalizeStringEvent>(songTemplate.titleId);
                Image icon = audioPlayer.GetUIComponent<Image>(songTemplate.iconId);
                RTLTextMeshPro titleText = title.GetComponent<RTLTextMeshPro>();

                title.StringReference = clipData.title;
                icon.sprite = clipData.icon;

                playButton.onClick.AddListener(() => AudioManager.Instance.Play(clipData.clip, index));
                playButton.onClick.AddListener(() => ToggleButton(playButton, pauseButton, true));

                pauseButton.onClick.AddListener(() => AudioManager.Instance.Pause());
                pauseButton.onClick.AddListener(() => ToggleButton(playButton, pauseButton, false));

                Button miniGameButton = audioPlayer.GetUIComponent<Button>(songTemplate.miniGameButtonId);
                if (clipData.chosenMiniGames == MiniGameName.None)
                {
                    miniGameButton.interactable = false;
                    return;
                }
                miniGameButton.onClick.AddListener(delegate
                {
                    MenuGenerator.Instance.OpenMiniGamePanel(new List<string> { clipData.chosenMiniGames.ToString() });
                    GameDataManager.Instance.SelectedMiniGameData = clipData.miniGameData;
                });
            }
            else
            {
                Debug.LogError("Failed to create audio player UI element.");
            }
        }

        private void ToggleButton(Button playButton, Button pauseButton, bool play)
        {
            bool playToggle = play ? false : true;

            playButton.gameObject.SetActive(playToggle);
            pauseButton.gameObject.SetActive(!playToggle);
        }
    }

    [System.Serializable]
    public class SimpleMenuContainer
    {
        public GameObject prefab;
        public EmptyVariable openId;
        public EmptyVariable closeId;
    }
}