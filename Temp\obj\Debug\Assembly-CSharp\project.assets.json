{"version": 3, "targets": {".NETStandard,Version=v2.1": {"FullscreenEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/FullscreenEditor.dll": {}}, "runtime": {"bin/placeholder/FullscreenEditor.dll": {}}}}}, "libraries": {"FullscreenEditor/1.0.0": {"type": "project", "path": "FullscreenEditor.csproj", "msbuildProject": "FullscreenEditor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["FullscreenEditor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity\\Team\\HappinessBox\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\Unity\\Team\\HappinessBox\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity\\Team\\HappinessBox\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity\\Team\\HappinessBox\\FullscreenEditor.csproj": {"projectPath": "D:\\Unity\\Team\\HappinessBox\\FullscreenEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413\\RuntimeIdentifierGraph.json"}}}}